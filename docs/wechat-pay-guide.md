# 微信小程序支付集成指南

## 概述

本项目已集成微信支付功能，支持微信小程序下单支付和申请退款。基于微信支付 API v3 实现，使用官方 Java SDK。

## 功能特性

- ✅ 微信小程序 JSAPI 支付
- ✅ 支付结果查询
- ✅ 订单关闭
- ✅ 申请退款
- ✅ 退款结果查询
- ✅ 支付回调处理
- ✅ 退款回调处理
- ✅ 财务明细记录
- ✅ 订单状态管理

## 配置说明

### 1. 微信支付配置

在 `application-dev.yml` 中配置微信支付参数：

```yaml
wechat:
  pay:
    # 微信小程序APPID
    appid: wx1234567890abcdef
    # 商户号
    mchid: 1234567890
    # 商户私钥路径(相对于resources目录)
    private-key-path: cert/apiclient_key.pem
    # 商户证书序列号
    merchant-serial-number: 1234567890ABCDEF1234567890ABCDEF12345678
    # 微信支付平台证书路径(相对于resources目录)
    wechat-pay-certificate-path: cert/wechatpay_certificate.pem
    # APIv3密钥
    api-v3-key: your-api-v3-key-32-characters-long
    # 支付回调地址
    notify-url: http://localhost:8080/wechat/pay/notify
    # 退款回调地址
    refund-notify-url: http://localhost:8080/wechat/refund/notify
```

### 2. 证书文件

将微信支付证书文件放置在 `src/main/resources/cert/` 目录下：

- `apiclient_key.pem` - 商户私钥文件
- `wechatpay_certificate.pem` - 微信支付平台证书（可选，SDK会自动下载）

## API 接口

### 1. 创建支付订单

**接口地址：** `POST /system/order/pay`

**请求参数：**
- `orderId` (Long) - 订单ID
- `openid` (String) - 用户openid

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "prepayId": "wx123456789",
    "appId": "wx1234567890abcdef",
    "timeStamp": "1640995200",
    "nonceStr": "abc123",
    "packageValue": "prepay_id=wx123456789",
    "signType": "RSA",
    "paySign": "signature_string"
  }
}
```

### 2. 申请退款

**接口地址：** `POST /system/order/refund`

**请求参数：**
- `orderId` (Long) - 订单ID
- `reason` (String, 可选) - 退款原因

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "refundId": "50000000382019052709732678859",
    "outRefundNo": "ORDER123_REFUND_456",
    "status": "SUCCESS",
    "amount": {
      "total": 100,
      "refund": 100
    }
  }
}
```

### 3. 微信支付专用接口

#### 查询支付订单
`GET /wechat/pay/query/{outTradeNo}`

#### 关闭支付订单
`POST /wechat/pay/close/{outTradeNo}`

#### 查询退款
`GET /wechat/pay/refund/query/{outRefundNo}`

## 小程序端调用示例

```javascript
// 1. 调用后端接口创建支付订单
wx.request({
  url: 'https://your-domain.com/system/order/pay',
  method: 'POST',
  data: {
    orderId: 123,
    openid: 'user_openid'
  },
  success: function(res) {
    if (res.data.code === 200) {
      // 2. 调起微信支付
      wx.requestPayment({
        timeStamp: res.data.data.timeStamp,
        nonceStr: res.data.data.nonceStr,
        package: res.data.data.packageValue,
        signType: res.data.data.signType,
        paySign: res.data.data.paySign,
        success: function(payRes) {
          console.log('支付成功', payRes);
          // 支付成功处理
        },
        fail: function(payRes) {
          console.log('支付失败', payRes);
          // 支付失败处理
        }
      });
    }
  }
});
```

## 订单状态说明

| 状态码 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 待支付 | 订单已创建，等待支付 |
| 1 | 已支付 | 支付成功 |
| 2 | 已完成 | 订单完成 |
| 3 | 已取消 | 订单已取消 |
| 4 | 已退款 | 已退款 |

## 注意事项

1. **证书安全**：商户私钥文件需要妥善保管，不要提交到代码仓库
2. **回调地址**：确保回调地址可以被微信服务器访问
3. **金额单位**：所有金额均以分为单位
4. **订单号唯一性**：商户订单号必须在商户系统内唯一
5. **异步处理**：支付和退款结果以回调通知为准

## 测试环境

微信支付提供沙箱环境用于测试，需要：
1. 申请沙箱商户号
2. 使用沙箱环境的配置参数
3. 使用测试用的小程序APPID

## 常见问题

### Q: 支付时提示"商户号该产品权限未开通"
A: 需要在微信商户平台开通JSAPI支付产品权限

### Q: 回调通知验签失败
A: 检查商户证书序列号和APIv3密钥是否正确配置

### Q: 订单查询返回"订单不存在"
A: 确认商户订单号格式正确，且订单确实存在

## 相关文档

- [微信支付官方文档](https://pay.weixin.qq.com/wiki/doc/apiv3/index.shtml)
- [微信支付Java SDK](https://github.com/wechatpay-apiv3/wechatpay-java)
- [小程序支付接入指南](https://developers.weixin.qq.com/miniprogram/dev/api/payment/wx.requestPayment.html)
