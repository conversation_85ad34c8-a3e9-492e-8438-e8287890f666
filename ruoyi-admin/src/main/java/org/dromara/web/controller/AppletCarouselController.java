package org.dromara.web.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.system.domain.vo.AppletCarouselVo;
import org.dromara.system.domain.bo.AppletCarouselBo;
import org.dromara.system.service.IAppletCarouselService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 轮播图
 *
 * <AUTHOR> Li
 * @date 2025-06-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/carousel")
public class AppletCarouselController extends BaseController {

    private final IAppletCarouselService appletCarouselService;

    /**
     * 查询轮播图列表
     */
//    @SaCheckPermission("system:carousel:list")
    @GetMapping("/list")
    public TableDataInfo<AppletCarouselVo> list(AppletCarouselBo bo, PageQuery pageQuery) {
        return appletCarouselService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出轮播图列表
     */
//    @SaCheckPermission("system:carousel:export")
    @Log(title = "轮播图", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppletCarouselBo bo, HttpServletResponse response) {
        List<AppletCarouselVo> list = appletCarouselService.queryList(bo);
        ExcelUtil.exportExcel(list, "轮播图", AppletCarouselVo.class, response);
    }

    /**
     * 获取轮播图详细信息
     *
     * @param carouselId 主键
     */
//    @SaCheckPermission("system:carousel:query")
    @GetMapping("/{carouselId}")
    public R<AppletCarouselVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long carouselId) {
        return R.ok(appletCarouselService.queryById(carouselId));
    }

    /**
     * 新增轮播图
     */
    @SaCheckPermission("system:carousel:add")
    @Log(title = "轮播图", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppletCarouselBo bo) {
        return toAjax(appletCarouselService.insertByBo(bo));
    }

    /**
     * 修改轮播图
     */
    @SaCheckPermission("system:carousel:edit")
    @Log(title = "轮播图", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppletCarouselBo bo) {
        return toAjax(appletCarouselService.updateByBo(bo));
    }

    /**
     * 删除轮播图
     *
     * @param carouselIds 主键串
     */
    @SaCheckPermission("system:carousel:remove")
    @Log(title = "轮播图", businessType = BusinessType.DELETE)
    @DeleteMapping("/{carouselIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] carouselIds) {
        return toAjax(appletCarouselService.deleteWithValidByIds(List.of(carouselIds), true));
    }
}
