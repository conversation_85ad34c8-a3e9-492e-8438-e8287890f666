package org.dromara.web.controller;


import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.vo.AppletMemberRegistrationTrendVo;
import org.dromara.system.domain.vo.AppletStatisticOverviewVo;
import org.dromara.system.service.IAppletStatisticService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

/**
 * 后台统计
 *
 * <AUTHOR> Li
 * @date 2025-06-014
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/statistic")
public class AppletStatisticController extends BaseController {

    private final IAppletStatisticService appletStatisticService;

    /**
     * 获取统计首页所有概览数据 (顶部卡片 + 右侧会员活跃/新增 + 底部系统资源)
     */
    @GetMapping("/overview")
    public R<AppletStatisticOverviewVo> getStatisticOverview() {
        AppletStatisticOverviewVo overviewVo = appletStatisticService.getStatisticOverview();
        return R.ok(overviewVo);
    }

    /**
     * 获取会员注册趋势数据
     *
     * @param startDate 起始日期 (可选)
     * @param endDate 结束日期 (可选)
     * @return 每日注册人数列表
     */
    @GetMapping("/member/registration/trend")
    public R<List<AppletMemberRegistrationTrendVo>> getMemberRegistrationTrend(
        @RequestParam(required = false) LocalDate startDate,
        @RequestParam(required = false) LocalDate endDate) {

        // 如果没有提供日期，默认为最近7天
        if (endDate == null) {
            endDate = LocalDate.now();
        }
        if (startDate == null) {
            // 包含今天在内的前7天
            startDate = endDate.minusDays(6);
        }

        List<AppletMemberRegistrationTrendVo> trendList = appletStatisticService.getMemberRegistrationTrend(startDate, endDate);
        return R.ok(trendList);
    }

}
