package org.dromara.web.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.vo.WechatPayOrderVo;
import org.dromara.system.domain.vo.WechatRefundVo;
import org.dromara.system.service.IAppletOrderService;
import org.dromara.system.service.IWechatPayService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 微信支付控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/wechat/pay")
public class WechatPayController extends BaseController {

    private final IWechatPayService wechatPayService;
    private final IAppletOrderService appletOrderService;

    /**
     * 创建微信支付订单
     */
    @SaCheckPermission("system:order:pay")
    @Log(title = "微信支付", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public R<WechatPayOrderVo> createPayOrder(@NotNull(message = "订单ID不能为空") @RequestParam Long orderId,
                                              @NotBlank(message = "用户openid不能为空") @RequestParam String openid) {
        WechatPayOrderVo result = appletOrderService.createWechatPayOrder(orderId, openid);
        return R.ok(result);
    }

    /**
     * 查询支付订单状态
     */
    @SaCheckPermission("system:order:query")
    @GetMapping("/query/{outTradeNo}")
    public R<String> queryPayOrder(@PathVariable String outTradeNo) {
        String status = wechatPayService.queryPayOrder(outTradeNo);
        return R.ok(status);
    }

    /**
     * 关闭支付订单
     */
    @SaCheckPermission("system:order:edit")
    @Log(title = "关闭支付订单", businessType = BusinessType.UPDATE)
    @PostMapping("/close/{outTradeNo}")
    public R<Void> closePayOrder(@PathVariable String outTradeNo) {
        Boolean result = wechatPayService.closePayOrder(outTradeNo);
        return result ? R.ok() : R.fail("关闭订单失败");
    }

    /**
     * 申请退款
     */
    @SaCheckPermission("system:order:refund")
    @Log(title = "申请退款", businessType = BusinessType.UPDATE)
    @PostMapping("/refund")
    public R<WechatRefundVo> applyRefund(@NotNull(message = "订单ID不能为空") @RequestParam Long orderId,
                                         @RequestParam(required = false) String reason) {
        WechatRefundVo result = appletOrderService.applyRefund(orderId, reason);
        return R.ok(result);
    }

    /**
     * 查询退款状态
     */
    @SaCheckPermission("system:order:query")
    @GetMapping("/refund/query/{outRefundNo}")
    public R<WechatRefundVo> queryRefund(@PathVariable String outRefundNo) {
        WechatRefundVo result = wechatPayService.queryRefund(outRefundNo);
        return R.ok(result);
    }

    /**
     * 微信支付回调通知
     */
    @PostMapping("/notify")
    public String payNotify(HttpServletRequest request, @RequestBody String requestBody) {
        try {
            String signature = request.getHeader("Wechatpay-Signature");
            String serial = request.getHeader("Wechatpay-Serial");
            String timestamp = request.getHeader("Wechatpay-Timestamp");
            String nonce = request.getHeader("Wechatpay-Nonce");

            log.info("收到微信支付回调通知");
            return wechatPayService.handlePayNotify(requestBody, signature, serial, timestamp, nonce);

        } catch (Exception e) {
            log.error("处理微信支付回调异常", e);
            return "FAIL";
        }
    }

    /**
     * 微信退款回调通知
     */
    @PostMapping("/refund/notify")
    public String refundNotify(HttpServletRequest request, @RequestBody String requestBody) {
        try {
            String signature = request.getHeader("Wechatpay-Signature");
            String serial = request.getHeader("Wechatpay-Serial");
            String timestamp = request.getHeader("Wechatpay-Timestamp");
            String nonce = request.getHeader("Wechatpay-Nonce");

            log.info("收到微信退款回调通知");
            return wechatPayService.handleRefundNotify(requestBody, signature, serial, timestamp, nonce);

        } catch (Exception e) {
            log.error("处理微信退款回调异常", e);
            return "FAIL";
        }
    }
}
