package org.dromara.web.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.system.domain.vo.AppletFinancialDetailVo;
import org.dromara.system.domain.bo.AppletFinancialDetailBo;
import org.dromara.system.service.IAppletFinancialDetailService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 财务明细
 *
 * <AUTHOR> Li
 * @date 2025-06-05
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/financialDetail")
public class AppletFinancialDetailController extends BaseController {

    private final IAppletFinancialDetailService appletFinancialDetailService;

    /**
     * 查询财务明细列表
     */
    @SaCheckPermission("system:financialDetail:list")
    @GetMapping("/list")
    public TableDataInfo<AppletFinancialDetailVo> list(AppletFinancialDetailBo bo, PageQuery pageQuery) {
        return appletFinancialDetailService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出财务明细列表
     */
    @SaCheckPermission("system:financialDetail:export")
    @Log(title = "财务明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppletFinancialDetailBo bo, HttpServletResponse response) {
        List<AppletFinancialDetailVo> list = appletFinancialDetailService.queryList(bo);
        ExcelUtil.exportExcel(list, "财务明细", AppletFinancialDetailVo.class, response);
    }

    /**
     * 获取财务明细详细信息
     *
     * @param detailId 主键
     */
    @SaCheckPermission("system:financialDetail:query")
    @GetMapping("/{detailId}")
    public R<AppletFinancialDetailVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long detailId) {
        return R.ok(appletFinancialDetailService.queryById(detailId));
    }

    /**
     * 新增财务明细
     */
    @SaCheckPermission("system:financialDetail:add")
    @Log(title = "财务明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppletFinancialDetailBo bo) {
        return toAjax(appletFinancialDetailService.insertByBo(bo));
    }

    /**
     * 修改财务明细
     */
    @SaCheckPermission("system:financialDetail:edit")
    @Log(title = "财务明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppletFinancialDetailBo bo) {
        return toAjax(appletFinancialDetailService.updateByBo(bo));
    }

    /**
     * 删除财务明细
     *
     * @param detailIds 主键串
     */
    @SaCheckPermission("system:financialDetail:remove")
    @Log(title = "财务明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{detailIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] detailIds) {
        return toAjax(appletFinancialDetailService.deleteWithValidByIds(List.of(detailIds), true));
    }
}
