package org.dromara.web.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.system.domain.vo.AppletOrderVo;
import org.dromara.system.domain.vo.WechatPayOrderVo;
import org.dromara.system.domain.vo.WechatRefundVo;
import org.dromara.system.domain.bo.AppletOrderBo;
import org.dromara.system.service.IAppletOrderService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 订单主
 *
 * <AUTHOR> Li
 * @date 2025-06-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/order")
public class AppletOrderController extends BaseController {

    private final IAppletOrderService appletOrderService;

    /**
     * 查询订单主列表
     */
    @SaCheckPermission("system:order:list")
    @GetMapping("/list")
    public TableDataInfo<AppletOrderVo> list(AppletOrderBo bo, PageQuery pageQuery) {
        return appletOrderService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出订单主列表
     */
    @SaCheckPermission("system:order:export")
    @Log(title = "订单主", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppletOrderBo bo, HttpServletResponse response) {
        List<AppletOrderVo> list = appletOrderService.queryList(bo);
        ExcelUtil.exportExcel(list, "订单主", AppletOrderVo.class, response);
    }

    /**
     * 获取订单主详细信息
     *
     * @param orderId 主键
     */
    @SaCheckPermission("system:order:query")
    @GetMapping("/{orderId}")
    public R<AppletOrderVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long orderId) {
        return R.ok(appletOrderService.queryById(orderId));
    }

    /**
     * 新增订单主
     */
//    @SaCheckPermission("system:order:add")
    @Log(title = "订单主", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Long> add(@Validated(AddGroup.class) @RequestBody AppletOrderBo bo) {
        return R.ok(appletOrderService.insertByBo(bo));
    }

    /**
     * 修改订单主
     */
    @SaCheckPermission("system:order:edit")
    @Log(title = "订单主", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppletOrderBo bo) {
        return toAjax(appletOrderService.updateByBo(bo));
    }

    /**
     * 删除订单主
     *
     * @param orderIds 主键串
     */
    @SaCheckPermission("system:order:remove")
    @Log(title = "订单主", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] orderIds) {
        return toAjax(appletOrderService.deleteWithValidByIds(List.of(orderIds), true));
    }

    /**
     * 创建微信支付订单
     *
     * @param orderId 订单ID
     * @param openid  用户openid
     */
    @SaCheckPermission("system:order:pay")
    @Log(title = "创建支付订单", businessType = BusinessType.INSERT)
    @PostMapping("/pay")
    public R<WechatPayOrderVo> createPayOrder(@NotNull(message = "订单ID不能为空") @RequestParam Long orderId,
                                              @NotBlank(message = "用户openid不能为空") @RequestParam String openid) {
        WechatPayOrderVo result = appletOrderService.createWechatPayOrder(orderId, openid);
        return R.ok(result);
    }

    /**
     * 申请订单退款
     *
     * @param orderId 订单ID
     * @param reason  退款原因
     */
    @SaCheckPermission("system:order:refund")
    @Log(title = "申请退款", businessType = BusinessType.UPDATE)
    @PostMapping("/refund")
    public R<WechatRefundVo> applyRefund(@NotNull(message = "订单ID不能为空") @RequestParam Long orderId,
                                         @RequestParam(required = false) String reason) {
        WechatRefundVo result = appletOrderService.applyRefund(orderId, reason);
        return R.ok(result);
    }
}
