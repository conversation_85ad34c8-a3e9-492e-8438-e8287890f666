package org.dromara.test;

import org.dromara.system.domain.bo.WechatPayOrderBo;
import org.dromara.system.domain.bo.WechatRefundBo;
import org.dromara.system.utils.WechatPayUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 微信支付服务测试
 *
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class WechatPayServiceTest {

    @Test
    public void testGenerateOutTradeNo() {
        String outTradeNo = WechatPayUtils.generateOutTradeNo();
        assertNotNull(outTradeNo);
        assertTrue(outTradeNo.length() > 8);
        assertTrue(WechatPayUtils.isValidOutTradeNo(outTradeNo));
    }

    @Test
    public void testGenerateOutRefundNo() {
        String outTradeNo = "20231201123456789";
        String outRefundNo = WechatPayUtils.generateOutRefundNo(outTradeNo);
        assertNotNull(outRefundNo);
        assertTrue(outRefundNo.startsWith(outTradeNo + "_REFUND_"));
        assertTrue(WechatPayUtils.isValidOutRefundNo(outRefundNo));
    }

    @Test
    public void testYuanToFen() {
        assertEquals(100L, WechatPayUtils.yuanToFen(1.0));
        assertEquals(150L, WechatPayUtils.yuanToFen(1.5));
        assertEquals(0L, WechatPayUtils.yuanToFen(null));
    }

    @Test
    public void testFenToYuan() {
        assertEquals(1.0, WechatPayUtils.fenToYuan(100L));
        assertEquals(1.5, WechatPayUtils.fenToYuan(150L));
        assertEquals(0.0, WechatPayUtils.fenToYuan(null));
    }

    @Test
    public void testValidateOutTradeNo() {
        assertTrue(WechatPayUtils.isValidOutTradeNo("20231201123456"));
        assertTrue(WechatPayUtils.isValidOutTradeNo("ORDER_123456"));
        assertFalse(WechatPayUtils.isValidOutTradeNo(""));
        assertFalse(WechatPayUtils.isValidOutTradeNo(null));
        assertFalse(WechatPayUtils.isValidOutTradeNo("订单123")); // 包含中文
    }

    @Test
    public void testWechatPayOrderBo() {
        WechatPayOrderBo bo = new WechatPayOrderBo();
        bo.setOrderId(1L);
        bo.setOpenid("test_openid");
        bo.setDescription("测试商品");
        bo.setOutTradeNo("TEST_ORDER_123");
        bo.setTotalAmount(100L);

        assertNotNull(bo.getOrderId());
        assertEquals("test_openid", bo.getOpenid());
        assertEquals("测试商品", bo.getDescription());
        assertEquals("TEST_ORDER_123", bo.getOutTradeNo());
        assertEquals(100L, bo.getTotalAmount());
    }

    @Test
    public void testWechatRefundBo() {
        WechatRefundBo bo = new WechatRefundBo();
        bo.setOrderId(1L);
        bo.setOutTradeNo("TEST_ORDER_123");
        bo.setOutRefundNo("TEST_ORDER_123_REFUND_456");
        bo.setReason("用户申请退款");
        bo.setRefundAmount(100L);
        bo.setTotalAmount(100L);

        assertNotNull(bo.getOrderId());
        assertEquals("TEST_ORDER_123", bo.getOutTradeNo());
        assertEquals("TEST_ORDER_123_REFUND_456", bo.getOutRefundNo());
        assertEquals("用户申请退款", bo.getReason());
        assertEquals(100L, bo.getRefundAmount());
        assertEquals(100L, bo.getTotalAmount());
    }
}
