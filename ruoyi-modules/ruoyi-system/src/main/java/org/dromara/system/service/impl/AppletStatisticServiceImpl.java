package org.dromara.system.service.impl;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.system.domain.vo.AppletMemberRegistrationTrendVo;
import org.dromara.system.domain.vo.AppletStatisticOverviewVo;
import org.dromara.system.service.IAppletStatisticService;
import org.dromara.system.service.ISysOssService;
import org.dromara.system.service.ISysUserService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 后台统计
 *
 * <AUTHOR>
 * @date 2025-06-14
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AppletStatisticServiceImpl implements IAppletStatisticService {

    private final ISysUserService sysUserService;
    private final ISysOssService sysOssService;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public AppletStatisticOverviewVo getStatisticOverview() {
        AppletStatisticOverviewVo overviewVo = new AppletStatisticOverviewVo();

        // 使用CompletableFuture并行执行，提高效率
        CompletableFuture<Void> totalMembersFuture = CompletableFuture.runAsync(() -> {
            overviewVo.setTotalMembers(sysUserService.countAllMembers());
        });

        CompletableFuture<Void> totalAttachmentsFuture = CompletableFuture.runAsync(() -> {
            overviewVo.setTotalAttachments(sysOssService.countAllFiles());
        });

        CompletableFuture<Void> totalAdminsFuture = CompletableFuture.runAsync(() -> {
            overviewVo.setTotalAdmins(sysUserService.countAllUsers());
        });

        // 暂时写死，实际需要根据业务逻辑获取
        CompletableFuture<Void> totalPluginsFuture = CompletableFuture.runAsync(() -> {
            overviewVo.setTotalPlugins(3L);
            overviewVo.setRunningPlugins(3L);
        });

        // 右侧用户活跃/新增概览 (需要RemoteMemberService提供更细致的统计方法)
        CompletableFuture<Void> memberActivityFuture = CompletableFuture.runAsync(() -> {
            LocalDate today = LocalDate.now();
            LocalDateTime todayStart = today.atStartOfDay();
            LocalDateTime todayEnd = today.atTime(LocalTime.MAX);

            // 假设 RemoteMemberService 提供这些方法
            overviewVo.setTodayRegistrations(sysUserService.countRegistrationsByDate(todayStart, todayEnd));
            overviewVo.setTodayLogins(sysUserService.countLoginsByDate(todayStart, todayEnd));
            overviewVo.setThreeDayNew(sysUserService.countRegistrationsByDate(today.minusDays(2).atStartOfDay(), todayEnd));
            overviewVo.setSevenDayNew(sysUserService.countRegistrationsByDate(today.minusDays(6).atStartOfDay(),  todayEnd));
            overviewVo.setSevenDayActive(sysUserService.countLoginsByDate(today.minusDays(6).atStartOfDay(), todayEnd));
            overviewVo.setMonthlyActive(sysUserService.countLoginsByDate(today.minusDays(29).atStartOfDay(), todayEnd));
        });

        // 底部系统资源统计
        CompletableFuture<Void> systemResourceFuture = CompletableFuture.runAsync(() -> {

            // 查询数据库表数量
            String countSql = sysOssService.countAllTables();
            Long tableCount = StrUtil.isEmpty(countSql) ? 0L : Long.parseLong(countSql);
            overviewVo.setDbTableCount(tableCount);

            // 查询数据库占用空间
            String sizeSql = sysOssService.sumAllTableSize();
            Long totalSize = StrUtil.isEmpty(sizeSql) ? 0L : Long.parseLong(sizeSql);

            // 转换为MB
            overviewVo.setDbSizeMB(BigDecimal.valueOf(totalSize / (1024.0 * 1024.0)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());

            // 附件和图片统计
            overviewVo.setAttachmentCount(sysOssService.countAllFiles());
            overviewVo.setAttachmentSizeMB(sysOssService.sumAllFileSizeInMB());
            overviewVo.setImageCount(sysOssService.countAllFiles());
            overviewVo.setImageSizeMB(sysOssService.sumAllFileSizeInMB());
        });


        // 等待所有异步任务完成
        try {
            CompletableFuture.allOf(
                totalMembersFuture,
                totalAttachmentsFuture,
                totalAdminsFuture,
                totalPluginsFuture,
                memberActivityFuture,
                systemResourceFuture
            ).get(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            // 捕获异常，可以是超时、执行失败等
            throw new ServiceException("获取统计概览数据失败: " + e.getMessage());
        }

        return overviewVo;
    }

    @Override
    public List<AppletMemberRegistrationTrendVo> getMemberRegistrationTrend(LocalDate startDate, LocalDate endDate) {
        Map<LocalDate, Long> dailyCounts = sysUserService.getDailyRegistrationCounts(startDate, endDate);

        List<AppletMemberRegistrationTrendVo> trendList = new ArrayList<>();
        LocalDate current = startDate;
        while (!current.isAfter(endDate)) {
            Long count = dailyCounts.getOrDefault(current, 0L);
            trendList.add(new AppletMemberRegistrationTrendVo(current, count));
            current = current.plusDays(1);
        }
        return trendList;
    }
}
