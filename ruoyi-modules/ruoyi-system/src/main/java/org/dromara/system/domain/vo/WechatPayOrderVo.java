package org.dromara.system.domain.vo;

import lombok.Data;

/**
 * 微信支付订单响应对象
 *
 * <AUTHOR>
 */
@Data
public class WechatPayOrderVo {

    /**
     * 预支付交易会话标识
     */
    private String prepayId;

    /**
     * 小程序调起支付API所需参数
     */
    private String appId;

    /**
     * 时间戳
     */
    private String timeStamp;

    /**
     * 随机字符串
     */
    private String nonceStr;

    /**
     * 订单详情扩展字符串
     */
    private String packageValue;

    /**
     * 签名类型
     */
    private String signType;

    /**
     * 签名
     */
    private String paySign;
}
