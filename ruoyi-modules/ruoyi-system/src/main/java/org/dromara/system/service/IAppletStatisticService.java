package org.dromara.system.service;

import org.dromara.system.domain.vo.AppletMemberRegistrationTrendVo;
import org.dromara.system.domain.vo.AppletStatisticOverviewVo;

import java.time.LocalDate;
import java.util.List;

/**
 * 后台统计
 *
 * <AUTHOR>
 * @date 2025-06-14
 */
public interface IAppletStatisticService {

    /**
     * 获取统计首页所有概览数据 (顶部卡片 + 右侧会员活跃/新增 + 底部系统资源)
     *
     * @return 统计概览VO
     */
    AppletStatisticOverviewVo getStatisticOverview();


    /**
     * 获取会员注册趋势数据
     *
     * @param startDate 起始日期
     * @param endDate   结束日期
     * @return 会员注册趋势数据列表
     */
    List<AppletMemberRegistrationTrendVo> getMemberRegistrationTrend(LocalDate startDate, LocalDate endDate);
}
