package org.dromara.system.domain.bo;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;

import java.math.BigDecimal;

/**
 * 微信支付订单请求对象
 *
 * <AUTHOR>
 */
@Data
public class WechatPayOrderBo {

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    /**
     * 用户openid
     */
    @NotBlank(message = "用户openid不能为空")
    private String openid;

    /**
     * 商品描述
     */
    @NotBlank(message = "商品描述不能为空")
    private String description;

    /**
     * 商户订单号
     */
    @NotBlank(message = "商户订单号不能为空")
    private String outTradeNo;

    /**
     * 订单金额，单位为分
     */
    @NotNull(message = "订单金额不能为空")
    @Min(value = 1, message = "订单金额必须大于0")
    private BigDecimal totalAmount;

    /**
     * 附加数据，在查询API和支付通知中原样返回
     */
    private String attach;

    /**
     * 订单失效时间，格式为yyyyMMddHHmmss
     */
    private String timeExpire;
}
