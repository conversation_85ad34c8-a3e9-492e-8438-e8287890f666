package org.dromara.system.service;

import org.dromara.system.domain.vo.AppletOrderVo;
import org.dromara.system.domain.vo.WechatPayOrderVo;
import org.dromara.system.domain.vo.WechatRefundVo;
import org.dromara.system.domain.bo.AppletOrderBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 订单主Service接口
 *
 * <AUTHOR> Li
 * @date 2025-06-09
 */
public interface IAppletOrderService {

    /**
     * 查询订单主
     *
     * @param orderId 主键
     * @return 订单主
     */
    AppletOrderVo queryById(Long orderId);

    /**
     * 分页查询订单主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单主分页列表
     */
    TableDataInfo<AppletOrderVo> queryPageList(AppletOrderBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的订单主列表
     *
     * @param bo 查询条件
     * @return 订单主列表
     */
    List<AppletOrderVo> queryList(AppletOrderBo bo);

    /**
     * 新增订单主
     *
     * @param bo 订单主
     * @return 是否新增成功
     */
    Long insertByBo(AppletOrderBo bo);

    /**
     * 修改订单主
     *
     * @param bo 订单主
     * @return 是否修改成功
     */
    Boolean updateByBo(AppletOrderBo bo);

    /**
     * 校验并批量删除订单主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 创建微信支付订单
     *
     * @param orderId 订单ID
     * @param openid  用户openid
     * @return 微信支付订单信息
     */
    WechatPayOrderVo createWechatPayOrder(Long orderId, String openid);

    /**
     * 处理支付成功回调
     *
     * @param outTradeNo    商户订单号
     * @param transactionId 微信支付订单号
     * @param tradeState    交易状态
     * @return 是否处理成功
     */
    Boolean handlePaySuccess(String outTradeNo, String transactionId, String tradeState);

    /**
     * 申请订单退款
     *
     * @param orderId 订单ID
     * @param reason  退款原因
     * @return 退款信息
     */
    WechatRefundVo applyRefund(Long orderId, String reason);

    /**
     * 处理退款成功回调
     *
     * @param outRefundNo 商户退款单号
     * @param refundId    微信退款单号
     * @param status      退款状态
     * @return 是否处理成功
     */
    Boolean handleRefundSuccess(String outRefundNo, String refundId, String status);
}
