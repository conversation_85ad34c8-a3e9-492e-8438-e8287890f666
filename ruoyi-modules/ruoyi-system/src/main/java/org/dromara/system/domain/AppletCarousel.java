package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import org.dromara.common.mybatis.core.domain.AppletEntity;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 轮播图对象 applet_carousel
 *
 * <AUTHOR>
 * @date 2025-06-02
 */
@Data
@TableName("applet_carousel")
public class AppletCarousel extends AppletEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 轮播图ID
     */
    @TableId(value = "carousel_id")
    private Long carouselId;

    /**
     * 轮播图标题
     */
    private String title;

    /**
     * 图片URL地址 (关联附件管理)
     */
    private String imageUrl;

    /**
     * 链接类型 (0无链接 1内部页面 2外部链接)
     */
    private String linkType;

    /**
     * 链接地址
     */
    private String linkUrl;

    /**
     * 显示顺序 (值越小越靠前)
     */
    private Long sortOrder;

    /**
     * 状态 (0启用 1停用)
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志 (0代表存在 2代表删除)
     */
    @TableLogic
    private String delFlag;

}
