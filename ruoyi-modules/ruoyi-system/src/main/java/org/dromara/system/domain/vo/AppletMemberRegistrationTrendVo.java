package org.dromara.system.domain.vo;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate; // 使用java.time.LocalDate更现代和方便

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 会员注册趋势数据VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppletMemberRegistrationTrendVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(name = "registerDate", description = "注册日期", example = "2025-06-08")
    private LocalDate registerDate;

    @Schema(name = "registerCount", description = "注册人数", example = "10")
    private Long registerCount;
}
