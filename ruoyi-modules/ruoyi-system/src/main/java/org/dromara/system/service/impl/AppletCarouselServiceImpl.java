package org.dromara.system.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.dromara.system.domain.bo.AppletCarouselBo;
import org.dromara.system.domain.vo.AppletCarouselVo;
import org.dromara.system.domain.AppletCarousel;
import org.dromara.system.mapper.AppletCarouselMapper;
import org.dromara.system.service.IAppletCarouselService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 轮播图Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-06-02
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AppletCarouselServiceImpl implements IAppletCarouselService {

    private final AppletCarouselMapper baseMapper;

    /**
     * 查询轮播图
     *
     * @param carouselId 主键
     * @return 轮播图
     */
    @Override
    public AppletCarouselVo queryById(Long carouselId){
        return baseMapper.selectVoById(carouselId);
    }

    /**
     * 分页查询轮播图列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 轮播图分页列表
     */
    @Override
    public TableDataInfo<AppletCarouselVo> queryPageList(AppletCarouselBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppletCarousel> lqw = buildQueryWrapper(bo);
        Page<AppletCarouselVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的轮播图列表
     *
     * @param bo 查询条件
     * @return 轮播图列表
     */
    @Override
    public List<AppletCarouselVo> queryList(AppletCarouselBo bo) {
        LambdaQueryWrapper<AppletCarousel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppletCarousel> buildQueryWrapper(AppletCarouselBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppletCarousel> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(AppletCarousel::getCarouselId);
        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), AppletCarousel::getTitle, bo.getTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getImageUrl()), AppletCarousel::getImageUrl, bo.getImageUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getLinkType()), AppletCarousel::getLinkType, bo.getLinkType());
        lqw.eq(StringUtils.isNotBlank(bo.getLinkUrl()), AppletCarousel::getLinkUrl, bo.getLinkUrl());
        lqw.eq(bo.getSortOrder() != null, AppletCarousel::getSortOrder, bo.getSortOrder());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppletCarousel::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增轮播图
     *
     * @param bo 轮播图
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(AppletCarouselBo bo) {
        AppletCarousel add = MapstructUtils.convert(bo, AppletCarousel.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setCarouselId(add.getCarouselId());
        }
        return flag;
    }

    /**
     * 修改轮播图
     *
     * @param bo 轮播图
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(AppletCarouselBo bo) {
        AppletCarousel update = MapstructUtils.convert(bo, AppletCarousel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppletCarousel entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除轮播图信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
