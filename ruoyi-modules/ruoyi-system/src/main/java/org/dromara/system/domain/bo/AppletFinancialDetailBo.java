package org.dromara.system.domain.bo;

import org.dromara.common.mybatis.core.domain.AppletEntity;
import org.dromara.system.domain.AppletFinancialDetail;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;

/**
 * 财务明细业务对象 applet_financial_detail
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AppletFinancialDetail.class, reverseConvertGenerate = false)
public class AppletFinancialDetailBo extends AppletEntity {

    /**
     * 明细ID
     */
    @NotNull(message = "明细ID不能为空", groups = { EditGroup.class })
    private Long detailId;

    /**
     * 关联会员ID (关联sys_user)
     */
    @NotNull(message = "关联会员ID (关联sys_user)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 会员用户编号
     */
    private String userName;

    /**
     * 会员手机号码
     */
    private String memberPhone;

    /**
     * 交易类型编码 (01充值 02消费 03提现 04退款 05佣金 99其他)
     */
    @NotBlank(message = "交易类型编码 (01充值 02消费 03提现 04退款 05佣金 99其他)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String transactionType;

    /**
     * 操作特征/交易描述 (如：微信支付入账，购买企业认证服务)
     */
    @NotBlank(message = "操作特征/交易描述 (如：微信支付入账，购买企业认证服务)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String transactionDescription;

    /**
     * 交易金额 (正数表示入账，负数表示出账)
     */
    @NotNull(message = "交易金额 (正数表示入账，负数表示出账)不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal amount;

    /**
     * 操作前会员账户余额
     */
    @NotNull(message = "操作前会员账户余额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal balanceBeforeOperation;

    /**
     * 操作后会员账户余额
     */
    @NotNull(message = "操作后会员账户余额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal balanceAfterOperation;

    /**
     * 关联订单ID (可空，如果非订单相关的资金变动)
     */
    private Long orderId;

    /**
     * 交易渠道 (01微信支付 02支付宝 03银行转账 04后台充值 99其他)
     */
    private String transactionChannel;

    /**
     * 交易状态 (0成功 1失败 2处理中)
     */
    private String transactionStatus;

    /**
     * 相关业务ID (如微信支付订单号、退款单号等)
     */
    private String relatedBizId;

    /**
     * 备注
     */
    private String remark;


}
