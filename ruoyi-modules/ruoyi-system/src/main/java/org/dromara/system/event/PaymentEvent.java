package org.dromara.system.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.context.ApplicationEvent;

/**
 * 支付事件
 *
 * <AUTHOR>
 */
@Data
public class PaymentEvent extends ApplicationEvent {

    /**
     * 商户订单号
     */
    private String outTradeNo;

    /**
     * 微信支付订单号
     */
    private String transactionId;

    /**
     * 交易状态
     */
    private String tradeState;

    /**
     * 事件类型
     */
    private EventType eventType;

    public PaymentEvent(Object source, String outTradeNo, String transactionId, String tradeState, EventType eventType) {
        super(source);
        this.outTradeNo = outTradeNo;
        this.transactionId = transactionId;
        this.tradeState = tradeState;
        this.eventType = eventType;
    }

    public enum EventType {
        /**
         * 支付成功
         */
        PAYMENT_SUCCESS,

        /**
         * 支付失败
         */
        PAYMENT_FAILED
    }
}
