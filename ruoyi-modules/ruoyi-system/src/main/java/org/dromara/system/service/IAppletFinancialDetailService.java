package org.dromara.system.service;

import org.dromara.system.domain.vo.AppletFinancialDetailVo;
import org.dromara.system.domain.bo.AppletFinancialDetailBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 财务明细Service接口
 *
 * <AUTHOR> Li
 * @date 2025-06-05
 */
public interface IAppletFinancialDetailService {

    /**
     * 查询财务明细
     *
     * @param detailId 主键
     * @return 财务明细
     */
    AppletFinancialDetailVo queryById(Long detailId);

    /**
     * 分页查询财务明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 财务明细分页列表
     */
    TableDataInfo<AppletFinancialDetailVo> queryPageList(AppletFinancialDetailBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的财务明细列表
     *
     * @param bo 查询条件
     * @return 财务明细列表
     */
    List<AppletFinancialDetailVo> queryList(AppletFinancialDetailBo bo);

    /**
     * 新增财务明细
     *
     * @param bo 财务明细
     * @return 是否新增成功
     */
    Boolean insertByBo(AppletFinancialDetailBo bo);

    /**
     * 修改财务明细
     *
     * @param bo 财务明细
     * @return 是否修改成功
     */
    Boolean updateByBo(AppletFinancialDetailBo bo);

    /**
     * 校验并批量删除财务明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
