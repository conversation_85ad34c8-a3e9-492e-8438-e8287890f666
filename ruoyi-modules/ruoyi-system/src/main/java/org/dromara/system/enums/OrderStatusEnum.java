package org.dromara.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrderStatusEnum {

    /**
     * 待支付
     */
    PENDING_PAYMENT("0", "待支付"),

    /**
     * 已支付
     */
    PAID("1", "已支付"),

    /**
     * 已完成
     */
    COMPLETED("2", "已完成"),

    /**
     * 已取消
     */
    CANCELLED("3", "已取消"),

    /**
     * 已退款
     */
    REFUNDED("4", "已退款");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 订单状态枚举
     */
    public static OrderStatusEnum getByCode(String code) {
        for (OrderStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否可以支付
     *
     * @param code 状态码
     * @return 是否可以支付
     */
    public static boolean canPay(String code) {
        return PENDING_PAYMENT.getCode().equals(code);
    }

    /**
     * 判断是否可以退款
     *
     * @param code 状态码
     * @return 是否可以退款
     */
    public static boolean canRefund(String code) {
        return PAID.getCode().equals(code) || COMPLETED.getCode().equals(code);
    }
}
