package org.dromara.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付渠道枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PayChannelEnum {

    /**
     * 微信支付
     */
    WECHAT("0", "微信支付"),

    /**
     * 支付宝
     */
    ALIPAY("1", "支付宝"),

    /**
     * 其他
     */
    OTHER("2", "其他");

    /**
     * 渠道码
     */
    private final String code;

    /**
     * 渠道描述
     */
    private final String description;

    /**
     * 根据渠道码获取枚举
     *
     * @param code 渠道码
     * @return 支付渠道枚举
     */
    public static PayChannelEnum getByCode(String code) {
        for (PayChannelEnum channel : values()) {
            if (channel.getCode().equals(code)) {
                return channel;
            }
        }
        return null;
    }
}
