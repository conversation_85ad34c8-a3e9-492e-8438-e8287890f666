package org.dromara.system.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.dromara.system.domain.bo.AppletFinancialDetailBo;
import org.dromara.system.domain.vo.AppletFinancialDetailVo;
import org.dromara.system.domain.AppletFinancialDetail;
import org.dromara.system.mapper.AppletFinancialDetailMapper;
import org.dromara.system.service.IAppletFinancialDetailService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 财务明细Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-06-05
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AppletFinancialDetailServiceImpl implements IAppletFinancialDetailService {

    private final AppletFinancialDetailMapper baseMapper;

    /**
     * 查询财务明细
     *
     * @param detailId 主键
     * @return 财务明细
     */
    @Override
    public AppletFinancialDetailVo queryById(Long detailId){
        return baseMapper.selectVoById(detailId);
    }

    /**
     * 分页查询财务明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 财务明细分页列表
     */
    @Override
    public TableDataInfo<AppletFinancialDetailVo> queryPageList(AppletFinancialDetailBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppletFinancialDetail> lqw = buildQueryWrapper(bo);
        Page<AppletFinancialDetailVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的财务明细列表
     *
     * @param bo 查询条件
     * @return 财务明细列表
     */
    @Override
    public List<AppletFinancialDetailVo> queryList(AppletFinancialDetailBo bo) {
        LambdaQueryWrapper<AppletFinancialDetail> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppletFinancialDetail> buildQueryWrapper(AppletFinancialDetailBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppletFinancialDetail> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(AppletFinancialDetail::getDetailId);
        lqw.eq(bo.getUserId() != null, AppletFinancialDetail::getUserId, bo.getUserId());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), AppletFinancialDetail::getUserName, bo.getUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getMemberPhone()), AppletFinancialDetail::getMemberPhone, bo.getMemberPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionType()), AppletFinancialDetail::getTransactionType, bo.getTransactionType());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionDescription()), AppletFinancialDetail::getTransactionDescription, bo.getTransactionDescription());
        lqw.eq(bo.getAmount() != null, AppletFinancialDetail::getAmount, bo.getAmount());
        lqw.eq(bo.getBalanceBeforeOperation() != null, AppletFinancialDetail::getBalanceBeforeOperation, bo.getBalanceBeforeOperation());
        lqw.eq(bo.getBalanceAfterOperation() != null, AppletFinancialDetail::getBalanceAfterOperation, bo.getBalanceAfterOperation());
        lqw.eq(bo.getOrderId() != null, AppletFinancialDetail::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionChannel()), AppletFinancialDetail::getTransactionChannel, bo.getTransactionChannel());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionStatus()), AppletFinancialDetail::getTransactionStatus, bo.getTransactionStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getRelatedBizId()), AppletFinancialDetail::getRelatedBizId, bo.getRelatedBizId());
        return lqw;
    }

    /**
     * 新增财务明细
     *
     * @param bo 财务明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(AppletFinancialDetailBo bo) {
        AppletFinancialDetail add = MapstructUtils.convert(bo, AppletFinancialDetail.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setDetailId(add.getDetailId());
        }
        return flag;
    }

    /**
     * 修改财务明细
     *
     * @param bo 财务明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(AppletFinancialDetailBo bo) {
        AppletFinancialDetail update = MapstructUtils.convert(bo, AppletFinancialDetail.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppletFinancialDetail entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除财务明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
