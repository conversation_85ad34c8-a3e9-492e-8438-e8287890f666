package org.dromara.system.domain.vo;

import org.dromara.common.mybatis.core.domain.AppletEntity;
import org.dromara.system.domain.AppletFinancialDetail;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;



/**
 * 财务明细视图对象 applet_financial_detail
 *
 * <AUTHOR> Li
 * @date 2025-06-05
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AppletFinancialDetail.class)
public class AppletFinancialDetailVo extends AppletEntity {

    /**
     * 明细ID
     */
    @ExcelProperty(value = "明细ID")
    private Long detailId;

    /**
     * 关联会员ID (关联sys_user)
     */
    @ExcelProperty(value = "关联会员ID (关联sys_user)")
    private Long userId;

    /**
     * 会员用户编号
     */
    @ExcelProperty(value = "会员用户编号")
    private String userName;

    /**
     * 会员手机号码
     */
    @ExcelProperty(value = "会员手机号码")
    private String memberPhone;

    /**
     * 交易类型编码 (01充值 02消费 03提现 04退款 05佣金 99其他)
     */
    @ExcelProperty(value = "交易类型编码 (01充值 02消费 03提现 04退款 05佣金 99其他)")
    private String transactionType;

    /**
     * 操作特征/交易描述 (如：微信支付入账，购买企业认证服务)
     */
    @ExcelProperty(value = "操作特征/交易描述 (如：微信支付入账，购买企业认证服务)")
    private String transactionDescription;

    /**
     * 交易金额 (正数表示入账，负数表示出账)
     */
    @ExcelProperty(value = "交易金额 (正数表示入账，负数表示出账)")
    private BigDecimal amount;

    /**
     * 操作前会员账户余额
     */
    @ExcelProperty(value = "操作前会员账户余额")
    private BigDecimal balanceBeforeOperation;

    /**
     * 操作后会员账户余额
     */
    @ExcelProperty(value = "操作后会员账户余额")
    private BigDecimal balanceAfterOperation;

    /**
     * 关联订单ID (可空，如果非订单相关的资金变动)
     */
    @ExcelProperty(value = "关联订单ID (可空，如果非订单相关的资金变动)")
    private Long orderId;

    /**
     * 交易渠道 (01微信支付 02支付宝 03银行转账 04后台充值 99其他)
     */
    @ExcelProperty(value = "交易渠道 (01微信支付 02支付宝 03银行转账 04后台充值 99其他)")
    private String transactionChannel;

    /**
     * 交易状态 (0成功 1失败 2处理中)
     */
    @ExcelProperty(value = "交易状态 (0成功 1失败 2处理中)")
    private String transactionStatus;

    /**
     * 相关业务ID (如微信支付订单号、退款单号等)
     */
    @ExcelProperty(value = "相关业务ID (如微信支付订单号、退款单号等)")
    private String relatedBizId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

}
