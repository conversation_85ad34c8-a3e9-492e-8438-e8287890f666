package org.dromara.system.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 微信支付工具类
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class WechatPayUtils {

    /**
     * 生成商户订单号
     * 格式：日期(8位) + 雪花ID(19位)
     *
     * @return 商户订单号
     */
    public static String generateOutTradeNo() {
        String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        String snowflakeId = IdUtil.getSnowflakeNextIdStr();
        return date + snowflakeId;
    }

    /**
     * 生成商户退款单号
     * 格式：原订单号 + "_REFUND_" + 雪花ID
     *
     * @param outTradeNo 原商户订单号
     * @return 商户退款单号
     */
    public static String generateOutRefundNo(String outTradeNo) {
        return outTradeNo + "_REFUND_" + IdUtil.getSnowflakeNextIdStr();
    }

    /**
     * 生成订单失效时间
     * 默认30分钟后失效
     *
     * @return 失效时间字符串，格式：yyyyMMddHHmmss
     */
    public static String generateTimeExpire() {
        return generateTimeExpire(30);
    }

    /**
     * 生成订单失效时间
     *
     * @param minutes 多少分钟后失效
     * @return 失效时间字符串，格式：yyyyMMddHHmmss
     */
    public static String generateTimeExpire(int minutes) {
        LocalDateTime expireTime = LocalDateTime.now().plusMinutes(minutes);
        return expireTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    }

    /**
     * 元转分
     *
     * @param yuan 元金额
     * @return 分金额
     */
    public static Long yuanToFen(Double yuan) {
        if (yuan == null) {
            return 0L;
        }
        return Math.round(yuan * 100);
    }

    /**
     * 分转元
     *
     * @param fen 分金额
     * @return 元金额
     */
    public static Double fenToYuan(Long fen) {
        if (fen == null) {
            return 0.0;
        }
        return fen / 100.0;
    }

    /**
     * 验证商户订单号格式
     *
     * @param outTradeNo 商户订单号
     * @return 是否有效
     */
    public static boolean isValidOutTradeNo(String outTradeNo) {
        if (outTradeNo == null || outTradeNo.length() < 6 || outTradeNo.length() > 32) {
            return false;
        }
        // 只能包含字母、数字、下划线、横杠
        return outTradeNo.matches("^[a-zA-Z0-9_-]+$");
    }

    /**
     * 验证退款单号格式
     *
     * @param outRefundNo 商户退款单号
     * @return 是否有效
     */
    public static boolean isValidOutRefundNo(String outRefundNo) {
        if (outRefundNo == null || outRefundNo.length() < 6 || outRefundNo.length() > 64) {
            return false;
        }
        // 只能包含字母、数字、下划线、横杠
        return outRefundNo.matches("^[a-zA-Z0-9_-]+$");
    }
}
