package org.dromara.system.domain.bo;

import org.dromara.common.mybatis.core.domain.AppletEntity;
import org.dromara.system.domain.AppletCarousel;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 轮播图业务对象 applet_carousel
 *
 * <AUTHOR> Li
 * @date 2025-06-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AppletCarousel.class, reverseConvertGenerate = false)
public class AppletCarouselBo extends AppletEntity {

    /**
     * 轮播图ID
     */
    @NotNull(message = "轮播图ID不能为空", groups = { EditGroup.class })
    private Long carouselId;

    /**
     * 轮播图标题
     */
    @NotBlank(message = "轮播图标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String title;

    /**
     * 图片URL地址 (关联附件管理)
     */
    @NotBlank(message = "图片URL地址 (关联附件管理)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String imageUrl;

    /**
     * 链接类型 (0无链接 1内部页面 2外部链接)
     */
    private String linkType;

    /**
     * 链接地址
     */
    private String linkUrl;

    /**
     * 显示顺序 (值越小越靠前)
     */
    private Long sortOrder;

    /**
     * 状态 (0启用 1停用)
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

}
