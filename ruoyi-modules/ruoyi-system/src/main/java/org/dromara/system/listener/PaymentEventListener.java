package org.dromara.system.listener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.system.event.PaymentEvent;
import org.dromara.system.event.RefundEvent;
import org.dromara.system.service.IAppletOrderService;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 支付事件监听器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PaymentEventListener {

    private final IAppletOrderService appletOrderService;

    /**
     * 处理支付事件
     */
    @Async
    @EventListener
    public void handlePaymentEvent(PaymentEvent event) {
        try {
            if (event.getEventType() == PaymentEvent.EventType.PAYMENT_SUCCESS) {
                appletOrderService.handlePaySuccess(event.getOutTradeNo(), event.getTransactionId(), event.getTradeState());
                log.info("支付成功事件处理完成，订单号：{}", event.getOutTradeNo());
            }
        } catch (Exception e) {
            log.error("处理支付事件异常，订单号：{}", event.getOutTradeNo(), e);
        }
    }

    /**
     * 处理退款事件
     */
    @Async
    @EventListener
    public void handleRefundEvent(RefundEvent event) {
        try {
            if (event.getEventType() == RefundEvent.EventType.REFUND_SUCCESS) {
                appletOrderService.handleRefundSuccess(event.getOutRefundNo(), event.getRefundId(), event.getStatus());
                log.info("退款成功事件处理完成，退款单号：{}", event.getOutRefundNo());
            }
        } catch (Exception e) {
            log.error("处理退款事件异常，退款单号：{}", event.getOutRefundNo(), e);
        }
    }
}
