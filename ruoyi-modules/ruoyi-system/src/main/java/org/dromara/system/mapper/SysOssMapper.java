package org.dromara.system.mapper;

import org.apache.ibatis.annotations.Select;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.system.domain.SysOss;
import org.dromara.system.domain.vo.SysOssVo;

/**
 * 文件上传 数据层
 *
 * <AUTHOR> Li
 */
public interface SysOssMapper extends BaseMapperPlus<SysOss, SysOssVo> {

    @Select("SELECT COUNT(*) FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE()")
    String countDatabaseTables();

    @Select("SELECT SUM(DATA_LENGTH + INDEX_LENGTH) FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE()")
    String sumDatabaseTableSize();

    @Select("SELECT COUNT(file_size) FROM sys_oss")
    Double sumFileSize();
}
