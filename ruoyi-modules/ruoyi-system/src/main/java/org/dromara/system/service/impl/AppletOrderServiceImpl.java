package org.dromara.system.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.domain.AppletFinancialDetail;
import org.dromara.system.domain.AppletOrder;
import org.dromara.system.domain.bo.AppletOrderBo;
import org.dromara.system.domain.bo.WechatPayOrderBo;
import org.dromara.system.domain.bo.WechatRefundBo;
import org.dromara.system.domain.vo.AppletOrderVo;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.domain.vo.WechatPayOrderVo;
import org.dromara.system.domain.vo.WechatRefundVo;
import org.dromara.system.enums.OrderStatusEnum;
import org.dromara.system.enums.PayChannelEnum;
import org.dromara.system.mapper.AppletFinancialDetailMapper;
import org.dromara.system.mapper.AppletOrderMapper;
import org.dromara.system.service.IAppletOrderService;
import org.dromara.system.service.ISysUserService;
import org.dromara.system.service.IWechatPayService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 订单主Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-06-09
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AppletOrderServiceImpl implements IAppletOrderService {

    private final AppletOrderMapper baseMapper;
    private final AppletFinancialDetailMapper financialDetailMapper;
    private final IWechatPayService wechatPayService;
    private final ISysUserService sysUserService;

    /**
     * 查询订单主
     *
     * @param orderId 主键
     * @return 订单主
     */
    @Override
    public AppletOrderVo queryById(Long orderId) {
        return baseMapper.selectVoById(orderId);
    }

    /**
     * 分页查询订单主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单主分页列表
     */
    @Override
    public TableDataInfo<AppletOrderVo> queryPageList(AppletOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppletOrder> lqw = buildQueryWrapper(bo);
        Page<AppletOrderVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的订单主列表
     *
     * @param bo 查询条件
     * @return 订单主列表
     */
    @Override
    public List<AppletOrderVo> queryList(AppletOrderBo bo) {
        LambdaQueryWrapper<AppletOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppletOrder> buildQueryWrapper(AppletOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppletOrder> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(AppletOrder::getOrderId);
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNumber()), AppletOrder::getOrderNumber, bo.getOrderNumber());
        lqw.eq(bo.getMemberUserId() != null, AppletOrder::getMemberUserId, bo.getMemberUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getMemberPhone()), AppletOrder::getMemberPhone, bo.getMemberPhone());
        lqw.eq(bo.getEmployeeUserId() != null, AppletOrder::getEmployeeUserId, bo.getEmployeeUserId());
        lqw.like(StringUtils.isNotBlank(bo.getEmployeeUserName()), AppletOrder::getEmployeeUserName, bo.getEmployeeUserName());
        lqw.eq(bo.getServiceId() != null, AppletOrder::getServiceId, bo.getServiceId());
        lqw.like(StringUtils.isNotBlank(bo.getServiceName()), AppletOrder::getServiceName, bo.getServiceName());
        lqw.eq(bo.getServicePrice() != null, AppletOrder::getServicePrice, bo.getServicePrice());
        lqw.eq(bo.getOrderAmount() != null, AppletOrder::getOrderAmount, bo.getOrderAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getCertificationStatus()), AppletOrder::getCertificationStatus, bo.getCertificationStatus());
        lqw.like(StringUtils.isNotBlank(bo.getCompanyName()), AppletOrder::getCompanyName, bo.getCompanyName());
        lqw.eq(StringUtils.isNotBlank(bo.getSocialCreditCode()), AppletOrder::getSocialCreditCode, bo.getSocialCreditCode());
        lqw.like(StringUtils.isNotBlank(bo.getLegalPersonName()), AppletOrder::getLegalPersonName, bo.getLegalPersonName());
        lqw.eq(StringUtils.isNotBlank(bo.getLegalIdentityCard()), AppletOrder::getLegalIdentityCard, bo.getLegalIdentityCard());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderStatus()), AppletOrder::getOrderStatus, bo.getOrderStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getPayChannel()), AppletOrder::getPayChannel, bo.getPayChannel());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionId()), AppletOrder::getTransactionId, bo.getTransactionId());
        lqw.eq(StringUtils.isNotBlank(bo.getReceiptImageUrl()), AppletOrder::getReceiptImageUrl, bo.getReceiptImageUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getVoucherImageUrl()), AppletOrder::getVoucherImageUrl, bo.getVoucherImageUrl());
        lqw.eq(bo.getPayTime() != null, AppletOrder::getPayTime, bo.getPayTime());
        lqw.eq(bo.getFinishTime() != null, AppletOrder::getFinishTime, bo.getFinishTime());
        lqw.eq(bo.getCancelTime() != null, AppletOrder::getCancelTime, bo.getCancelTime());
        lqw.eq(bo.getRefundTime() != null, AppletOrder::getRefundTime, bo.getRefundTime());
        return lqw;
    }

    /**
     * 新增订单主
     *
     * @param bo 订单主
     * @return 是否新增成功
     */
    @Override
    public Long insertByBo(AppletOrderBo bo) {
        AppletOrder add = MapstructUtils.convert(bo, AppletOrder.class);
        // 生成订单编号
        assert add != null;
        add.setOrderNumber(IdUtil.getSnowflakeNextIdStr());
        SysUserVo sysUserVo = sysUserService.selectUserById(add.getMemberUserId());
//        add.setEmployeeUserId(sysUserVo.getTenantId());
        add.setEmployeeUserName(sysUserVo.getUserName());
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setOrderId(add.getOrderId());
        }
        return add.getOrderId();
    }

    /**
     * 修改订单主
     *
     * @param bo 订单主
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(AppletOrderBo bo) {
        AppletOrder update = MapstructUtils.convert(bo, AppletOrder.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppletOrder entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除订单主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public WechatPayOrderVo createWechatPayOrder(Long orderId, String openid) {
        // 查询订单信息
        AppletOrder order = baseMapper.selectById(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        // 检查订单状态
        if (!OrderStatusEnum.canPay(order.getOrderStatus())) {
            throw new ServiceException("订单状态不允许支付");
        }

        // 检查是否已有支付记录
        if (StrUtil.isNotBlank(order.getTransactionId())) {
            throw new ServiceException("订单已支付，请勿重复支付");
        }

        // 构建微信支付请求
        WechatPayOrderBo payOrderBo = new WechatPayOrderBo();
        payOrderBo.setOrderId(orderId);
        payOrderBo.setOpenid(openid);
        payOrderBo.setDescription(order.getServiceName());
        payOrderBo.setOutTradeNo(order.getOrderNumber());
        payOrderBo.setTotalAmount(order.getOrderAmount());
        payOrderBo.setAttach(String.valueOf(orderId));
        log.info("微信支付请求参数：{}", JSONUtil.toJsonStr(payOrderBo));

        // 调用微信支付服务
        return wechatPayService.createPayOrder(payOrderBo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handlePaySuccess(String outTradeNo, String transactionId, String tradeState) {
        try {
            // 查询订单
            AppletOrder order = baseMapper.selectOne(
                new LambdaQueryWrapper<AppletOrder>()
                    .eq(AppletOrder::getOrderNumber, outTradeNo)
            );

            if (order == null) {
                log.error("支付回调处理失败：订单不存在，商户订单号：{}", outTradeNo);
                return false;
            }

            // 检查订单状态
            if (!OrderStatusEnum.PENDING_PAYMENT.getCode().equals(order.getOrderStatus())) {
                log.warn("订单状态不是待支付，跳过处理，订单号：{}，当前状态：{}", outTradeNo, order.getOrderStatus());
                return true;
            }

            // 更新订单状态
            AppletOrder updateOrder = new AppletOrder();
            updateOrder.setOrderId(order.getOrderId());
            updateOrder.setOrderStatus(OrderStatusEnum.PAID.getCode());
            updateOrder.setPayChannel(PayChannelEnum.WECHAT.getCode());
            updateOrder.setTransactionId(transactionId);
            updateOrder.setPayTime(new Date());

            boolean updateResult = baseMapper.updateById(updateOrder) > 0;

            if (updateResult) {
                // 记录财务明细
                recordFinancialDetail(order, "02", "微信支付入账", order.getOrderAmount(), "01", "0", transactionId);
                log.info("订单支付成功处理完成，订单号：{}", outTradeNo);
            }

            return updateResult;

        } catch (Exception e) {
            log.error("处理支付成功回调异常，订单号：{}", outTradeNo, e);
            throw e;
        }
    }

    @Override
    public WechatRefundVo applyRefund(Long orderId, String reason) {
        // 查询订单信息
        AppletOrder order = baseMapper.selectById(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        // 检查订单状态
        if (!OrderStatusEnum.canRefund(order.getOrderStatus())) {
            throw new ServiceException("订单状态不允许退款");
        }

        // 检查是否已退款
        if (OrderStatusEnum.REFUNDED.getCode().equals(order.getOrderStatus())) {
            throw new ServiceException("订单已退款，请勿重复申请");
        }

        // 构建退款请求
        WechatRefundBo refundBo = new WechatRefundBo();
        refundBo.setOrderId(orderId);
        refundBo.setTransactionId(order.getTransactionId());
        refundBo.setOutTradeNo(order.getOrderNumber());
        refundBo.setOutRefundNo(order.getOrderNumber() + "_REFUND_" + IdUtil.getSnowflakeNextIdStr());
        refundBo.setReason(StrUtil.isNotBlank(reason) ? reason : "用户申请退款");
        refundBo.setRefundAmount(order.getOrderAmount());
        refundBo.setTotalAmount(order.getOrderAmount());

        // 调用微信退款服务
        return wechatPayService.createRefund(refundBo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handleRefundSuccess(String outRefundNo, String refundId, String status) {
        try {
            // 从退款单号中提取订单号
            String outTradeNo = outRefundNo.substring(0, outRefundNo.indexOf("_REFUND_"));

            // 查询订单
            AppletOrder order = baseMapper.selectOne(
                new LambdaQueryWrapper<AppletOrder>()
                    .eq(AppletOrder::getOrderNumber, outTradeNo)
            );

            if (order == null) {
                log.error("退款回调处理失败：订单不存在，商户订单号：{}", outTradeNo);
                return false;
            }

            // 更新订单状态
            AppletOrder updateOrder = new AppletOrder();
            updateOrder.setOrderId(order.getOrderId());
            updateOrder.setOrderStatus(OrderStatusEnum.REFUNDED.getCode());
            updateOrder.setRefundTime(new Date());

            boolean updateResult = baseMapper.updateById(updateOrder) > 0;

            if (updateResult) {
                // 记录财务明细
                recordFinancialDetail(order, "04", "微信支付退款", order.getOrderAmount(), "01", "0", refundId);
                log.info("订单退款成功处理完成，订单号：{}", outTradeNo);
            }

            return updateResult;

        } catch (Exception e) {
            log.error("处理退款成功回调异常，退款单号：{}", outRefundNo, e);
            throw e;
        }
    }

    /**
     * 记录财务明细
     */
    private void recordFinancialDetail(AppletOrder order, String transactionType, String description,
                                       BigDecimal amount, String channel, String status, String relatedBizId) {
        AppletFinancialDetail detail = new AppletFinancialDetail();
        detail.setUserId(order.getMemberUserId());
        detail.setUserName(order.getMemberPhone());
        detail.setMemberPhone(order.getMemberPhone());
        detail.setTransactionType(transactionType);
        detail.setTransactionDescription(description);
        detail.setAmount(amount);
        detail.setOrderId(order.getOrderId());
        detail.setTransactionChannel(channel);
        detail.setTransactionStatus(status);
        detail.setRelatedBizId(relatedBizId);
        detail.setRemark("订单号：" + order.getOrderNumber());

        // 这里需要查询用户当前余额，计算操作前后余额
        // 为简化示例，这里设置为0，实际应用中需要查询用户账户余额
        detail.setBalanceBeforeOperation(BigDecimal.ZERO);
        detail.setBalanceAfterOperation(BigDecimal.ZERO);

        financialDetailMapper.insert(detail);
    }
}

