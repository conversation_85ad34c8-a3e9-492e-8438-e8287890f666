package org.dromara.system.domain.vo;

import lombok.Data;

/**
 * 微信退款响应对象
 *
 * <AUTHOR>
 */
@Data
public class WechatRefundVo {

    /**
     * 微信支付退款单号
     */
    private String refundId;

    /**
     * 商户退款单号
     */
    private String outRefundNo;

    /**
     * 微信支付订单号
     */
    private String transactionId;

    /**
     * 商户订单号
     */
    private String outTradeNo;

    /**
     * 退款渠道
     */
    private String channel;

    /**
     * 退款入账账户
     */
    private String userReceivedAccount;

    /**
     * 退款成功时间
     */
    private String successTime;

    /**
     * 退款状态
     */
    private String status;

    /**
     * 资金账户
     */
    private String fundsAccount;

    /**
     * 金额信息
     */
    private AmountInfo amount;

    /**
     * 金额信息
     */
    @Data
    public static class AmountInfo {
        /**
         * 订单金额，单位为分
         */
        private Long total;

        /**
         * 退款金额，单位为分
         */
        private Long refund;

        /**
         * 用户支付金额，单位为分
         */
        private Long payerTotal;

        /**
         * 用户退款金额，单位为分
         */
        private Long payerRefund;
    }
}
