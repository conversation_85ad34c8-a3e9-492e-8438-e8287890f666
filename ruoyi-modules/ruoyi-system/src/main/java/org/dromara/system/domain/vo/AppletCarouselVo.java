package org.dromara.system.domain.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import org.dromara.common.mybatis.core.domain.AppletEntity;
import org.dromara.system.domain.AppletCarousel;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 轮播图视图对象 applet_carousel
 *
 * <AUTHOR> Li
 * @date 2025-06-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AppletCarousel.class)
public class AppletCarouselVo extends AppletEntity {

    /**
     * 轮播图ID
     */
    @ExcelProperty(value = "轮播图ID")
    private Long carouselId;

    /**
     * 轮播图标题
     */
    @ExcelProperty(value = "轮播图标题")
    private String title;

    /**
     * 图片URL地址 (关联附件管理)
     */
    @ExcelProperty(value = "图片URL地址 (关联附件管理)")
    private String imageUrl;

    /**
     * 链接类型 (0无链接 1内部页面 2外部链接)
     */
    @ExcelProperty(value = "链接类型 (0无链接 1内部页面 2外部链接)")
    private String linkType;

    /**
     * 链接地址
     */
    @ExcelProperty(value = "链接地址")
    private String linkUrl;

    /**
     * 显示顺序 (值越小越靠前)
     */
    @ExcelProperty(value = "显示顺序 (值越小越靠前)")
    private Long sortOrder;

    /**
     * 状态 (0启用 1停用)
     */
    @ExcelProperty(value = "状态 (0启用 1停用)")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

}
