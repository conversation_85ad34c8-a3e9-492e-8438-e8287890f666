package org.dromara.system.config;

import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.payments.jsapi.JsapiServiceExtension;
import com.wechat.pay.java.service.refund.RefundService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 微信支付配置
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "wechat.pay")
public class WechatPayConfig {

    /**
     * 微信小程序APPID
     */
    private String appid;

    /**
     * 商户号
     */
    private String mchid;

    /**
     * 商户私钥路径
     */
    private String privateKeyPath;

    /**
     * 商户证书序列号
     */
    private String merchantSerialNumber;

    /**
     * 微信支付平台证书路径
     */
    private String wechatPayCertificatePath;

    /**
     * APIv3密钥
     */
    private String apiV3Key;

    /**
     * 支付回调地址
     */
    private String notifyUrl;

    /**
     * 退款回调地址
     */
    private String refundNotifyUrl;

    /**
     * 微信支付配置
     */
    @Bean
    public Config config() {
        try {
            // 读取商户私钥
            String privateKey = readPrivateKey();

            // 使用自动更新平台证书的RSA配置
            // 一个商户号只能初始化一个配置，否则会因为重复的下载任务报错
            return new RSAAutoCertificateConfig.Builder()
                .merchantId(mchid)
                .privateKey(privateKey)
                .merchantSerialNumber(merchantSerialNumber)
                .apiV3Key(apiV3Key)
                .build();
        } catch (Exception e) {
            log.error("微信支付配置初始化失败", e);
            throw new RuntimeException("微信支付配置初始化失败", e);
        }
    }

    /**
     * JSAPI支付服务
     */
    @Bean
    public JsapiServiceExtension jsapiService(Config config) {
        return new JsapiServiceExtension.Builder().config(config).build();
    }

    /**
     * 退款服务
     */
    @Bean
    public RefundService refundService(Config config) {
        return new RefundService.Builder().config(config).build();
    }

    /**
     * 读取商户私钥
     */
    private String readPrivateKey() throws IOException {
        try {
            // 尝试从classpath读取
            ClassPathResource resource = new ClassPathResource(privateKeyPath);
            if (resource.exists()) {
                return new String(resource.getInputStream().readAllBytes());
            }
        } catch (Exception e) {
            log.warn("从classpath读取私钥失败，尝试从文件系统读取: {}", e.getMessage());
        }

        // 从文件系统读取
        return Files.readString(Paths.get(privateKeyPath));
    }
}
