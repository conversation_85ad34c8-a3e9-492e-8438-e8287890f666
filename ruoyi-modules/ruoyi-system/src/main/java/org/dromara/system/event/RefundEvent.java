package org.dromara.system.event;

import lombok.Data;
import org.springframework.context.ApplicationEvent;

/**
 * 退款事件
 *
 * <AUTHOR>
 */
@Data
public class RefundEvent extends ApplicationEvent {

    /**
     * 商户退款单号
     */
    private String outRefundNo;

    /**
     * 微信退款单号
     */
    private String refundId;

    /**
     * 退款状态
     */
    private String status;

    /**
     * 事件类型
     */
    private EventType eventType;

    public RefundEvent(Object source, String outRefundNo, String refundId, String status, EventType eventType) {
        super(source);
        this.outRefundNo = outRefundNo;
        this.refundId = refundId;
        this.status = status;
        this.eventType = eventType;
    }

    public enum EventType {
        /**
         * 退款成功
         */
        REFUND_SUCCESS,
        
        /**
         * 退款失败
         */
        REFUND_FAILED
    }
}
