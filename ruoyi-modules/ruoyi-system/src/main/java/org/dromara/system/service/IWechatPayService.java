package org.dromara.system.service;

import org.dromara.system.domain.bo.WechatPayOrderBo;
import org.dromara.system.domain.bo.WechatRefundBo;
import org.dromara.system.domain.vo.WechatPayOrderVo;
import org.dromara.system.domain.vo.WechatRefundVo;

/**
 * 微信支付服务接口
 *
 * <AUTHOR>
 */
public interface IWechatPayService {

    /**
     * 创建微信支付订单
     *
     * @param payOrderBo 支付订单请求参数
     * @return 支付订单响应
     */
    WechatPayOrderVo createPayOrder(WechatPayOrderBo payOrderBo);

    /**
     * 查询微信支付订单
     *
     * @param outTradeNo 商户订单号
     * @return 支付订单信息
     */
    String queryPayOrder(String outTradeNo);

    /**
     * 关闭微信支付订单
     *
     * @param outTradeNo 商户订单号
     * @return 是否成功
     */
    Boolean closePayOrder(String outTradeNo);

    /**
     * 申请退款
     *
     * @param refundBo 退款请求参数
     * @return 退款响应
     */
    WechatRefundVo createRefund(WechatRefundBo refundBo);

    /**
     * 查询退款
     *
     * @param outRefundNo 商户退款单号
     * @return 退款信息
     */
    WechatRefundVo queryRefund(String outRefundNo);

    /**
     * 处理支付回调
     *
     * @param requestBody 回调请求体
     * @param signature 签名
     * @param serial 证书序列号
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @return 处理结果
     */
    String handlePayNotify(String requestBody, String signature, String serial, String timestamp, String nonce);

    /**
     * 处理退款回调
     *
     * @param requestBody 回调请求体
     * @param signature 签名
     * @param serial 证书序列号
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @return 处理结果
     */
    String handleRefundNotify(String requestBody, String signature, String serial, String timestamp, String nonce);
}
