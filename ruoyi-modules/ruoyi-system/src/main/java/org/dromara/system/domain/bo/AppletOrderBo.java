package org.dromara.system.domain.bo;

import org.dromara.common.mybatis.core.domain.AppletEntity;
import org.dromara.system.domain.AppletOrder;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 订单主业务对象 applet_order
 *
 * <AUTHOR> Li
 * @date 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AppletOrder.class, reverseConvertGenerate = false)
public class AppletOrderBo extends AppletEntity {

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空", groups = { EditGroup.class })
    private Long orderId;

    /**
     * 订单编号 (系统生成唯一ID，对应原型图“订单号”)
     */
//    @NotBlank(message = "订单编号 (系统生成唯一ID，对应原型图“订单号”)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderNumber;

    /**
     * 下单会员ID (关联t_member)
     */
    @NotNull(message = "下单会员ID (关联t_member)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long memberUserId;

    /**
     * 下单会员手机号码 (下单时快照，对应原型图“手机号”)
     */
    private String memberPhone;

    /**
     * 下单时会员绑定的员工用户ID快照 (关联sys_user)
     */
    private Long employeeUserId;

    /**
     * 员工认证码
     */
    private String employeeUserName;

    /**
     * 关联的服务项目ID (关联t_service_item)
     */
//    @NotNull(message = "关联的服务项目ID (关联t_service_item)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long serviceId;

    /**
     * 下单时服务项目名称快照 (对应原型图“服务项目”)
     */
    private String serviceName;

    /**
     * 下单时服务项目单价快照 (即该服务项目的原始价格)
     */
    @NotNull(message = "下单时服务项目单价快照 (即该服务项目的原始价格)不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal servicePrice;

    /**
     * 订单总金额 (实际支付或应支付的总金额，对应原型图“营业额”)
     */
    @NotNull(message = "订单总金额 (实际支付或应支付的总金额，对应原型图“营业额”)不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal orderAmount;

    /**
     * 会员自身认证状态 (下单时快照，0待认证 1认证中 2已认证 3认证失败)
     */
    private String certificationStatus;

    /**
     * 下单公司名称 (下单时快照，对应原型图“公司名称”)
     */
    private String companyName;

    /**
     * 下单公司社会信用代码 (下单时快照，对应原型图“社会信息代码”)
     */
    private String socialCreditCode;

    /**
     * 下单公司法人姓名 (下单时快照，对应原型图“法人”)
     */
    private String legalPersonName;

    /**
     * 下单公司法人身份证号 (下单时快照，对应原型图“法人”)
     */
    private String legalIdentityCard;

    /**
     * 订单状态 (0待支付 1已支付 2已完成 3已取消 4已退款)
     */
//    @NotBlank(message = "订单状态 (0待支付 1已支付 2已完成 3已取消 4已退款)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderStatus;

    /**
     * 支付渠道 (0微信支付 1支付宝 2其他)
     */
    private String payChannel;

    /**
     * 第三方支付交易ID (如微信支付的交易号)
     */
    private String transactionId;

    /**
     * 回执图片URL (用于存储回执凭证图片地址)
     */
    private String receiptImageUrl;

    /**
     * 凭证图片URL (用于存储凭证图片地址)
     */
    private String voucherImageUrl;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 取消时间
     */
    private Date cancelTime;

    /**
     * 退款时间
     */
    private Date refundTime;

    /**
     * 备注
     */
    private String remark;


}
