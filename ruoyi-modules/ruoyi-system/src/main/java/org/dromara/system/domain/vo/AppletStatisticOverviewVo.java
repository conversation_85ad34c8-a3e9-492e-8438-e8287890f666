package org.dromara.system.domain.vo;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 统计首页总览数据VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppletStatisticOverviewVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    // 顶部总览卡片
    @Schema(name = "totalMembers", description = "总会员数", example = "50")
    private Long totalMembers;

    @Schema(name = "totalPlugins", description = "总插件数", example = "10")
    private Long totalPlugins;

    @Schema(name = "totalAttachments", description = "总附件数", example = "200")
    private Long totalAttachments;

    @Schema(name = "totalAdmins", description = "总管理员数", example = "5")
    private Long totalAdmins;

    // 右侧用户活跃/新增概览
    @Schema(name = "todayRegistrations", description = "今日注册", example = "5")
    private Long todayRegistrations;

    @Schema(name = "todayLogins", description = "今日登录", example = "20")
    private Long todayLogins;

    @Schema(name = "threeDayNew", description = "三日新增（近3天）", example = "15")
    private Long threeDayNew;

    @Schema(name = "sevenDayNew", description = "七日新增（近7天）", example = "30")
    private Long sevenDayNew;

    @Schema(name = "sevenDayActive", description = "七日活跃（去重）", example = "100")
    private Long sevenDayActive;

    @Schema(name = "monthlyActive", description = "月活跃（去重）", example = "500")
    private Long monthlyActive;

    // 底部系统资源统计
    @Schema(name = "runningPlugins", description = "运行中的插件数", example = "3")
    private Long runningPlugins;

    @Schema(name = "dbTableCount", description = "数据库表数量", example = "50")
    private Long dbTableCount;

    @Schema(name = "dbSizeMB", description = "数据库占用空间（MB）", example = "1024.5")
    private Double dbSizeMB;

    @Schema(name = "attachmentCount", description = "附件数量（实时统计）", example = "150")
    private Long attachmentCount;

    @Schema(name = "attachmentSizeMB", description = "附件大小（MB）", example = "512.0")
    private Double attachmentSizeMB;

    @Schema(name = "imageCount", description = "图片数量", example = "300")
    private Long imageCount;

    @Schema(name = "imageSizeMB", description = "图片大小（MB）", example = "256.3")
    private Double imageSizeMB;

}
