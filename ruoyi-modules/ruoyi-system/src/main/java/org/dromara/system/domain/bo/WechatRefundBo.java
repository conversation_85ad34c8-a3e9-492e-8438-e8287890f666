package org.dromara.system.domain.bo;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;

import java.math.BigDecimal;

/**
 * 微信退款请求对象
 *
 * <AUTHOR>
 */
@Data
public class WechatRefundBo {

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    /**
     * 微信支付订单号
     */
    private String transactionId;

    /**
     * 商户订单号
     */
    private String outTradeNo;

    /**
     * 商户退款单号
     */
    @NotBlank(message = "商户退款单号不能为空")
    private String outRefundNo;

    /**
     * 退款原因
     */
    private String reason;

    /**
     * 退款金额，单位为分
     */
    @NotNull(message = "退款金额不能为空")
    @Min(value = 1, message = "退款金额必须大于0")
    private BigDecimal refundAmount;

    /**
     * 原订单金额，单位为分
     */
    @NotNull(message = "原订单金额不能为空")
    @Min(value = 1, message = "原订单金额必须大于0")
    private BigDecimal totalAmount;

    /**
     * 退款资金来源
     */
    private String fundsAccount;
}
